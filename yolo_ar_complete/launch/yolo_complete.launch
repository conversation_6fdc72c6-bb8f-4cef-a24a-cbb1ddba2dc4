<?xml version="1.0"?>
<launch>
  <!-- 参数配置 -->
  <arg name="enable_yolo" default="true" doc="use yolo?" />
  <arg name="enable_fusion" default="true" doc="jizhon huati use?" />
  <arg name="enable_ksh" default="true" doc="是否启用ksh输出" />

  <!-- YOLO模型配置 -->
  <arg name="model_path" default="$(find yolo_ar_complete)/../../../yolo_model/best.pt" doc="YOLO model" />
  <arg name="confidence_threshold" default="0.5" doc="YOLOzhixingdu" />
  <arg name="nms_threshold" default="0.4" doc="NMSyuzhi" />
  
  <!-- 话题配置 -->
  <arg name="camera_topic" default="/hand_camera/image_raw" doc="摄像头图像话题" />
  <arg name="real_ar_topic" default="/hand_camera/ar_pose_marker" doc="真AR码话题" />
  <arg name="fusion_output_topic" default="/ar_yolo_fusion/ar_pose_marker" doc="集中输出话题" />
  <arg name="ksh_topic" default="/yolo_ksh" doc="YOLO ksh图像话题" />
  
  <!-- 坐标系配置 -->
  <arg name="camera_frame" default="hand_camera_link" doc="摄像头坐标系" />
  
  <!-- 相机参数 -->
  <arg name="camera_fx" default="429.0" doc="相机内参fx" />
  <arg name="camera_fy" default="429.0" doc="相机内参fy" />
  <arg name="camera_cx" default="360.5" doc="相机内参cx" />
  <arg name="camera_cy" default="320.5" doc="相机内参cy" />
  <group if="$(arg enable_yolo)">
    <!--yolo物块识别-->
    <node name="yolo_detector" pkg="yolo_ar_complete" type="yolo_detector_python.py" output="screen">
      <param name="model_path" value="$(arg model_path)" />
      <param name="confidence_threshold" value="$(arg confidence_threshold)" />
      <param name="nms_threshold" value="$(arg nms_threshold)" />
      <param name="camera_topic" value="$(arg camera_topic)" />
      <!-- ksh参数 -->
      <param name="enable_ksh" value="$(arg enable_ksh)" />
      <param name="ksh_topic" value="$(arg ksh_topic)" />
    </node>
    
    <!-- yolo转为ar码 -->
    <node name="yolo_ar_tracker" pkg="yolo_ar_complete" type="yolo_ar_tracker_node" output="screen">
      <param name="camera_frame" value="$(arg camera_frame)" />
      <param name="camera_fx" value="$(arg camera_fx)" />
      <param name="camera_fy" value="$(arg camera_fy)" />
      <param name="camera_cx" value="$(arg camera_cx)" />
      <param name="camera_cy" value="$(arg camera_cy)" />

      <!-- 高度偏移量-->
      <param name="height_offset" value="0.00"/> 

      <!-- 物体到AR码ID的映射 -->
      <rosparam>
        object_to_ar_mapping:
          jintou: 30
          mada: 31
          shext: 32
          xiaoxp: 33
          xingpian: 34
          zhuanzi: 35
      </rosparam>
    </node>
  </group>
  
  <group if="$(arg enable_fusion)">
    <!-- AR码与YOLO话题集中-->
    <node name="yolo_ar_fusion" pkg="yolo_ar_complete" type="yolo_ar_fusion_node" output="screen">
      <param name="real_ar_topic" value="$(arg real_ar_topic)" />
      <param name="yolo_ar_topic" value="/yolo_ar_markers" />
      <param name="fusion_topic" value="$(arg fusion_output_topic)" />
      <param name="target_frame" value="$(arg camera_frame)" />
      <param name="data_timeout" value="6.0" />
    </node>
  </group>
  <!-- 信息显示 -->
  <node name="system_info" pkg="rostopic" type="rostopic"
        args="echo -n 1 $(arg fusion_output_topic)" output="screen"
        launch-prefix="bash -c 'sleep 5; echo YOLO max topic:; '" />
  <!-- ksh信息显示 -->
  <group if="$(arg enable_ksh)">
    <node name="ksh_info" pkg="rostopic" type="rostopic"
          args="hz $(arg ksh_topic)" output="screen"
          launch-prefix="bash -c 'sleep 8; echo YOLO ksh topic frequency:; '" />
  </group>
        
</launch>
