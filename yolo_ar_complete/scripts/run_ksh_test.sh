#!/bin/bash

# YOLO KSH功能测试脚本

echo "=== YOLO KSH功能测试 ==="
echo ""

# 检查图片文件
IMAGE_PATH="$HOME/YOLO (1)/shiji.png"
if [ ! -f "$IMAGE_PATH" ]; then
    echo "❌ 错误: 找不到测试图片 $IMAGE_PATH"
    echo "请确保 shiji.png 文件在工作空间根目录中"
    exit 1
fi

echo "✅ 找到测试图片: $IMAGE_PATH"

# 检查模型文件
MODEL_PATH="$HOME/YOLO (1)/yolo_model/best.pt"
if [ ! -f "$MODEL_PATH" ]; then
    echo "❌ 错误: 找不到YOLO模型文件 $MODEL_PATH"
    echo "请确保模型文件存在"
    exit 1
fi

echo "✅ 找到YOLO模型: $MODEL_PATH"
echo ""

# 检查ROS环境
if [ -z "$ROS_MASTER_URI" ]; then
    echo "❌ 错误: ROS环境未设置"
    echo "请运行: source /opt/ros/noetic/setup.bash"
    exit 1
fi

echo "✅ ROS环境已设置"
echo ""

echo "🚀 启动测试..."
echo ""
echo "测试步骤:"
echo "1. 启动YOLO系统 (在新终端中运行)"
echo "   roslaunch yolo_ar_complete yolo_complete.launch enable_ksh:=true"
echo ""
echo "2. 等待系统启动完成后，运行图像测试"
echo "   rosrun yolo_ar_complete test_image_ksh.py"
echo ""
echo "3. 或者直接查看ksh话题"
echo "   rostopic echo /yolo_ksh"
echo ""

# 询问用户是否要自动启动
read -p "是否要自动启动YOLO系统? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动YOLO系统..."
    cd "$HOME/YOLO (1)"
    roslaunch yolo_ar_complete yolo_complete.launch enable_ksh:=true
else
    echo ""
    echo "请手动启动YOLO系统:"
    echo "cd '$HOME/YOLO (1)'"
    echo "roslaunch yolo_ar_complete yolo_complete.launch enable_ksh:=true"
    echo ""
    echo "然后在另一个终端运行测试:"
    echo "cd '$HOME/YOLO (1)'"
    echo "rosrun yolo_ar_complete test_image_ksh.py"
fi
