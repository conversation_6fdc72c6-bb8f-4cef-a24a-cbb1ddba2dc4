#!/usr/bin/env python3
"""
快速测试YOLO ksh功能 - 直接调用YOLO检测
"""
import cv2
import numpy as np
import os
import sys

def test_yolo_ksh():
    """直接测试YOLO检测和ksh可视化"""
    
    # 图片路径
    image_path = os.path.expanduser('~/YOLO (1)/shiji.png')
    
    print("=== YOLO KSH快速测试 ===")
    print(f"图片路径: {image_path}")
    
    # 检查图片文件
    if not os.path.exists(image_path):
        print(f"❌ 错误: 找不到图片文件 {image_path}")
        return False
    
    # 加载图片
    try:
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 错误: 无法读取图片 {image_path}")
            return False
        
        print(f"✅ 成功加载图片，尺寸: {image.shape[1]}x{image.shape[0]}")
        
    except Exception as e:
        print(f"❌ 加载图片错误: {e}")
        return False
    
    # 模拟YOLO检测结果（用于测试ksh可视化）
    print("🔍 模拟YOLO检测...")
    
    # 创建模拟检测结果
    detections = [
        {
            'class_name': 'jintou',
            'confidence': 0.85,
            'bbox': [100, 100, 200, 200],
            'center': [150, 150],
            'corners': [[100, 100], [200, 100], [200, 200], [100, 200]],
            'size': [100, 100]
        },
        {
            'class_name': 'mada',
            'confidence': 0.72,
            'bbox': [300, 150, 450, 280],
            'center': [375, 215],
            'corners': [[300, 150], [450, 150], [450, 280], [300, 280]],
            'size': [150, 130]
        }
    ]
    
    print(f"✅ 模拟检测到 {len(detections)} 个物体")
    
    # 应用ksh可视化
    ksh_image = apply_ksh_visualization(image.copy(), detections)
    
    # 显示结果
    print("🖼️  显示结果...")
    cv2.imshow('Original Image', image)
    cv2.imshow('YOLO KSH Result', ksh_image)
    
    print("按任意键继续，按 'q' 退出")
    key = cv2.waitKey(0) & 0xFF
    cv2.destroyAllWindows()
    
    if key == ord('q'):
        print("用户退出")
        return False
    
    print("✅ 测试完成!")
    return True

def apply_ksh_visualization(image, detections):
    """应用ksh可视化效果"""
    
    # 颜色定义
    colors = {
        'jintou': (0, 255, 0),    # 绿色
        'mada': (255, 0, 0),      # 蓝色
        'shext': (0, 0, 255),     # 红色
        'xiaoxp': (255, 255, 0),  # 青色
        'xingpian': (255, 0, 255), # 紫色
        'zhuanzi': (0, 255, 255)  # 黄色
    }
    
    for det in detections:
        # 获取边界框坐标
        x1, y1, x2, y2 = det['bbox']
        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
        
        # 获取类别和颜色
        class_name = det['class_name']
        color = colors.get(class_name, (128, 128, 128))  # 默认灰色
        confidence = det['confidence']
        
        # 绘制边界框
        cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)
        
        # 准备标签文本
        label = f"{class_name}: {confidence:.2f}"
        
        # 计算文本尺寸
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2
        (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
        
        # 绘制标签背景
        cv2.rectangle(image,
                     (x1, y1 - text_height - baseline - 5),
                     (x1 + text_width, y1),
                     color, -1)
        
        # 绘制标签文本
        cv2.putText(image, label,
                   (x1, y1 - baseline - 2),
                   font, font_scale, (255, 255, 255), thickness)
        
        # 绘制中心点
        center_x = int((x1 + x2) / 2)
        center_y = int((y1 + y2) / 2)
        cv2.circle(image, (center_x, center_y), 3, color, -1)
    
    return image

if __name__ == '__main__':
    try:
        success = test_yolo_ksh()
        if success:
            print("🎉 KSH可视化功能测试成功!")
        else:
            print("❌ KSH可视化功能测试失败!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"❌ 测试错误: {e}")
        sys.exit(1)
