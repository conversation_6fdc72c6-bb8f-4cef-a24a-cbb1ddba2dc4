"""
yolo物块识别
"""
import rospy
import cv2
import numpy as np
import os
import sys
import time
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
from yolo_ar_complete.msg import YoloDetection, YoloDetections
from geometry_msgs.msg import Point
TORCH_AVAILABLE = False
ULTRALYTICS_AVAILABLE = False
try:
    import torch
    TORCH_AVAILABLE = True
    rospy.loginfo("PyTorch yes!!!!!!!!!!!!!!!!")
except ImportError:
    rospy.logwarn("PyTorch no!!!!!!!!!!!!!!!!!")
try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
    rospy.loginfo("Ultralytics yes!!!!!!!!!!!!!!!!!")
except ImportError:
    rospy.logwarn("Ultralytics no!!!!!!!!!!!!!!!!")
class YoloDetectorPython:
    def __init__(self):
        rospy.init_node('yolo_detector_python', anonymous=True)
        self.load_parameters()
        self.bridge = CvBridge()
        self.model = None
        self.initialized = False
        #标签列表
        self.class_names = ['mada', 'shext', 'jintou', 'zhuanzi', 'xingpian', 'xiaoxp']
        # 初始化YOLO模型
        if self.initialize_model():
            # 订阅者和发布者
            self.image_sub = rospy.Subscriber(self.camera_topic, Image, self.image_callback)
            self.detection_pub = rospy.Publisher('/yolo_detections', YoloDetections, queue_size=1)
            # ksh
            if self.enable_ksh:
                self.ksh_pub = rospy.Publisher(self.ksh_topic, Image, queue_size=1)
                rospy.loginfo("[YOLO] ksh topic: %s", self.ksh_topic)
            rospy.loginfo("[yolo init yes!!!!!!!!!!!!!!!!!!!!!!!!!!")
        else:
            rospy.logerr("yolo init no!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
            sys.exit(1)
    def load_parameters(self):
        # 模型参数
        self.model_path = rospy.get_param('~model_path', 
                                         os.path.expanduser('~/ros_workspace/yolo_model/best.pt'))
        self.confidence_threshold = rospy.get_param('~confidence_threshold', 0.5)
        self.nms_threshold = rospy.get_param('~nms_threshold', 0.4)
        self.input_size = rospy.get_param('~input_size', 640)
        # 话题参数
        self.camera_topic = rospy.get_param('~camera_topic', '/hand_camera/image_raw')
        # 可视化
        self.enable_ksh = rospy.get_param('~enable_ksh', True)
        self.ksh_topic = rospy.get_param('~ksh_topic', '/yolo_ksh')
        rospy.loginfo("yolo jiazai yes!!!!!!!!!!!!!!!")
        rospy.loginfo("yolo model: %s", self.model_path)
        rospy.loginfo("yolo yes or no?: %.2f", self.confidence_threshold)
        rospy.loginfo("ksh enabled: %s", self.enable_ksh)
    def initialize_model(self):
        """初始化YOLO模型"""
        try:
            # 检查模型文件在不在
            if not os.path.exists(self.model_path):
                rospy.logerr("YOLO model no have!!!!!!!!!!!!!!!!!!!!1: %s", self.model_path)
                return False
            # 优先使用Ultralytics
            if ULTRALYTICS_AVAILABLE:
                rospy.loginfo("[YOLO] use Ultralytics")
                self.model = YOLO(self.model_path)
                rospy.loginfo("[YOLO] Ultralytics OK")
            elif TORCH_AVAILABLE:
                rospy.loginfo("[YOLO] use PyTorch")
                self.model = torch.hub.load('ultralytics/yolov5', 'custom', path=self.model_path)
                self.model.conf = self.confidence_threshold
                self.model.iou = self.nms_threshold
                rospy.loginfo("[YOLO] PyTorch ok")
            else:
                rospy.logerr("[YOLO] no install ultralytics or torch")
                return False
            self.initialized = True
            return True
        except Exception as e:
            rospy.logerr("[YOLO] model init no!!!!!!!!!!!!!!!: %s", str(e))
            return False
    def image_callback(self, msg):
        if not self.initialized:
            return
        try:
            # 转换ROS to OpenCV
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            # 执行检测
            start_time = time.time()
            detections = self.detect_objects(cv_image)
            processing_time = (time.time() - start_time) * 1000
            # 发布检测结果
            self.publish_detections(detections, cv_image.shape, processing_time, msg.header)
            # 发布ksh图像
            if self.enable_ksh:
                self.publish_ksh(cv_image, detections, msg.header)
        except Exception as e:
            rospy.logerr("[YOLO] tuxiang jc no!!!!!!!!!!!!!!!!!!: %s", str(e))
    def detect_objects(self, image):
        detections = []
        try:
            # 使用Ultralytics检测
            if ULTRALYTICS_AVAILABLE and hasattr(self.model, 'predict'):
                results = self.model.predict(image, conf=self.confidence_threshold, verbose=False)
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            # 获取边界框
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0].cpu().numpy())
                            class_id = int(box.cls[0].cpu().numpy())
                            # 获取类别
                            if class_id < len(self.class_names):
                                class_name = self.class_names[class_id]
                                # 计算4角
                                center_x = (x1 + x2) / 2.0
                                center_y = (y1 + y2) / 2.0
                                width = x2 - x1
                                height = y2 - y1
                                # 计算四个角点
                                corners = [
                                    [center_x - width/2, center_y - height/2],  # 左上
                                    [center_x + width/2, center_y - height/2],  # 右上
                                    [center_x + width/2, center_y + height/2],  # 右下
                                    [center_x - width/2, center_y + height/2]   # 左下
                                ]
                                detection = {
                                    'class_name': class_name,
                                    'confidence': confidence,
                                    'bbox': [x1, y1, x2, y2],
                                    'center': [center_x, center_y],
                                    'corners': corners, 
                                    'size': [width, height]
                                }
                                detections.append(detection)
            # Python脚本
            elif self.use_python_script_fallback():
                detections = self.detect_with_python_script(image)
            #PyTorch Hub
            elif TORCH_AVAILABLE:
                results = self.model(image)
                for *box, conf, cls in results.xyxy[0].cpu().numpy():
                    if conf > self.confidence_threshold:
                        class_id = int(cls)
                        if class_id < len(self.class_names):
                            class_name = self.class_names[class_id]
                            x1, y1, x2, y2 = box
                            center_x = (x1 + x2) / 2.0
                            center_y = (y1 + y2) / 2.0
                            width = x2 - x1
                            height = y2 - y1
                            # 四个角点
                            corners = [
                                [center_x - width/2, center_y - height/2],  # 左上
                                [center_x + width/2, center_y - height/2],  # 右上
                                [center_x + width/2, center_y + height/2],  # 右下
                                [center_x - width/2, center_y + height/2]   # 左下
                            ]
                            detection = {
                                'class_name': class_name,
                                'confidence': float(conf),
                                'bbox': box,
                                'center': [center_x, center_y],
                                'corners': corners,
                                'size': [width, height]
                            }
                            detections.append(detection)
        except Exception as e:
            rospy.logerr("[YOLO] jc no!!!!!!!!!!!!11: %s", str(e))
        return detections
    def publish_detections(self, detections, image_shape, processing_time, header):
        """发布检测结果"""
        msg = YoloDetections()
        msg.header = header
        msg.image_width = image_shape[1]
        msg.image_height = image_shape[0]
        msg.processing_time_ms = processing_time
        for det in detections:
            detection_msg = YoloDetection()
            detection_msg.class_name = det['class_name']
            detection_msg.confidence = det['confidence']
            # 边界框
            x1, y1, x2, y2 = det['bbox']
            # 中心点
            if 'center' in det:
                detection_msg.center.x = det['center'][0]
                detection_msg.center.y = det['center'][1]
            else:
                detection_msg.center.x = (x1 + x2) / 2.0
                detection_msg.center.y = (y1 + y2) / 2.0
            detection_msg.center.z = 0.0
            # 边界框
            detection_msg.bbox_min.x = x1
            detection_msg.bbox_min.y = y1
            detection_msg.bbox_min.z = 0.0
            detection_msg.bbox_max.x = x2
            detection_msg.bbox_max.y = y2
            detection_msg.bbox_max.z = 0.0
            # 四个角点
            if 'corners' in det:
                for i, corner in enumerate(det['corners']):
                    detection_msg.corners[i].x = corner[0]
                    detection_msg.corners[i].y = corner[1]
                    detection_msg.corners[i].z = 0.0
            # 边界框尺寸
            if 'size' in det:
                detection_msg.width = det['size'][0]
                detection_msg.height = det['size'][1]
            else:
                detection_msg.width = x2 - x1
                detection_msg.height = y2 - y1
            msg.detections.append(detection_msg)
        self.detection_pub.publish(msg)
        if len(detections) > 0:
            rospy.loginfo("[YOLO] have %d  wukuai，time: %.1fms",
                          len(detections), processing_time)
    def use_python_script_fallback(self):
        # 如果Ultralytics坏了
        return not ULTRALYTICS_AVAILABLE or rospy.get_param('~use_python_script', False)
    def detect_with_python_script(self, image):
        detections = []
        try:
            import tempfile
            import subprocess
            import os
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                temp_image_path = temp_file.name
                cv2.imwrite(temp_image_path, image)
            python_script = os.path.join(os.path.dirname(self.model_path), '..', '..', 'yolo_detect.py')
            if not os.path.exists(python_script):
                python_script = os.path.expanduser('~/ros_workspace/yolo_detect.py')
            command = [
                'python3', python_script,
                self.model_path,
                temp_image_path,
                str(self.confidence_threshold)
            ]
            rospy.logdebug("[YOLO] RUN: %s", ' '.join(command))
            result = subprocess.run(command, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                detections = self.parse_script_results(result.stdout, image.shape)
            else:
                rospy.logerr("[YOLO] Python shib: %s", result.stderr)
            os.unlink(temp_image_path)
        except Exception as e:
            rospy.logerr("[YOLO] Python error: %s", str(e))
        return detections
    def parse_script_results(self, script_output, image_shape):
        """解析Python结果"""
        detections = []
        height, width = image_shape[:2] 
        for line in script_output.strip().split('\n'):
            if line.startswith('#') or not line.strip():
                continue
            try:
                parts = line.split()
                if len(parts) >= 6:
                    class_name = parts[0]
                    confidence = float(parts[1])
                    x = int(float(parts[2]))
                    y = int(float(parts[3]))
                    width = int(float(parts[4]))
                    height = int(float(parts[5]))
                    x = max(0, min(x, width - 1))
                    y = max(0, min(y, height - 1))
                    bbox_width = max(1, min(width, width - x))
                    bbox_height = max(1, min(height, height - y))
                    center_x = x + bbox_width / 2.0
                    center_y = y + bbox_height / 2.0
                    corners = [
                        [x, y],                           # 左上
                        [x + bbox_width, y],              # 右上
                        [x + bbox_width, y + bbox_height], # 右下
                        [x, y + bbox_height]              # 左下
                    ]
                    detection = {
                        'class_name': class_name,
                        'confidence': confidence,
                        'bbox': [x, y, x + bbox_width, y + bbox_height],  # xyxy格式
                        'center': [center_x, center_y],
                        'corners': corners,
                        'size': [bbox_width, bbox_height]
                    }
                    detections.append(detection)
            except (ValueError, IndexError) as e:
                rospy.logwarn("[YOLO] ERROR: %s, 行: %s", str(e), line)
        return detections
    def publish_ksh(self, image, detections, header):
        try:
            ksh_image = image.copy()
            colors = {
                'jintou': (0, 255, 0),    # 绿色
                'mada': (255, 0, 0),      # 蓝色
                'shext': (0, 0, 255),     # 红色
                'xiaoxp': (255, 255, 0),  # 青色
                'xingpian': (255, 0, 255), # 紫色
                'zhuanzi': (0, 255, 255)  # 黄色
            }
            for det in detections:
                # 边界框坐标
                x1, y1, x2, y2 = det['bbox']
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                class_name = det['class_name']
                color = colors.get(class_name, (128, 128, 128))  # 默认灰色
                confidence = det['confidence']
                cv2.rectangle(ksh_image, (x1, y1), (x2, y2), color, 2)
                label = f"{class_name}: {confidence:.2f}"
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.6
                thickness = 2
                (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
                cv2.rectangle(ksh_image,
                             (x1, y1 - text_height - baseline - 5),
                             (x1 + text_width, y1),
                             color, -1)
                cv2.putText(ksh_image, label,
                           (x1, y1 - baseline - 2),
                           font, font_scale, (255, 255, 255), thickness)
                center_x = int((x1 + x2) / 2)
                center_y = int((y1 + y2) / 2)
                cv2.circle(ksh_image, (center_x, center_y), 3, color, -1)
            # 在图像上添加检测信息和状态
            info_text = f"Detections: {len(detections)}"
            status_text = "YOLO Active" if len(detections) > 0 else "YOLO Monitoring"
            # 添加半透明背景
            overlay = ksh_image.copy()
            cv2.rectangle(overlay, (5, 5), (300, 60), (0, 0, 0), -1)
            cv2.addWeighted(overlay, 0.3, ksh_image, 0.7, 0, ksh_image)
            # 添加文本信息
            cv2.putText(ksh_image, info_text, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(ksh_image, status_text, (10, 55),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0) if len(detections) > 0 else (255, 255, 0), 1)
            # 转换为ROS图像消息并发布
            ksh_msg = self.bridge.cv2_to_imgmsg(ksh_image, "bgr8")
            ksh_msg.header = header
            self.ksh_pub.publish(ksh_msg)
        except Exception as e:
            rospy.logerr("[YOLO] ksh error: %s", str(e))
    def run(self):
        """运行检测器"""
        rospy.loginfo("[YOLO] RUNING")
        rospy.spin()
if __name__ == '__main__':
    try:
        detector = YoloDetectorPython()
        detector.run()
    except rospy.ROSInterruptException:
        pass
