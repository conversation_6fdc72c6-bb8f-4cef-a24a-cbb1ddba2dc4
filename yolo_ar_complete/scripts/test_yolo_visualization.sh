#!/bin/bash

# YOLO ksh功能测试脚本

echo "=== YOLO ksh功能测试 ==="
echo ""

# 检查ROS环境
if [ -z "$ROS_MASTER_URI" ]; then
    echo "错误: ROS环境未设置，请先运行 source /opt/ros/noetic/setup.bash"
    exit 1
fi

echo "1. 检查话题列表..."
echo "当前ROS话题:"
rostopic list | grep -E "(yolo|camera|image)" || echo "  未找到相关话题"
echo ""

echo "2. 检查YOLO检测话题..."
if rostopic list | grep -q "/yolo_detections"; then
    echo "  ✅ /yolo_detections 话题存在"
    echo "  最近的检测消息:"
    timeout 3 rostopic echo /yolo_detections -n 1 2>/dev/null || echo "    无消息或超时"
else
    echo "  ❌ /yolo_detections 话题不存在"
fi
echo ""

echo "3. 检查ksh话题..."
if rostopic list | grep -q "/yolo_ksh"; then
    echo "  ✅ /yolo_ksh 话题存在"
    echo "  话题信息:"
    rostopic info /yolo_ksh 2>/dev/null || echo "    无法获取话题信息"
    echo "  话题频率:"
    timeout 5 rostopic hz /yolo_ksh 2>/dev/null || echo "    无消息或超时"
else
    echo "  ❌ /yolo_ksh 话题不存在"
    echo "  提示: 请确保启动时设置 enable_ksh:=true"
fi
echo ""

echo "4. 检查相机话题..."
if rostopic list | grep -q "/hand_camera/image_raw"; then
    echo "  ✅ /hand_camera/image_raw 话题存在"
else
    echo "  ❌ /hand_camera/image_raw 话题不存在"
    echo "  提示: 请确保相机节点正在运行"
fi
echo ""

echo "5. 建议的启动命令:"
echo "  启动完整系统: roslaunch yolo_ar_complete yolo_complete.launch"
echo "  查看ksh: rosrun image_view image_view image:=/yolo_ksh"
echo "  或使用测试脚本: rosrun yolo_ar_complete test_visualization.py"
echo ""

echo "=== 测试完成 ==="
