#!/usr/bin/env python3
"""
测试YOLO ksh功能的脚本
"""
import rospy
import cv2
from sensor_msgs.msg import Image
from cv_bridge import CvBridge

class KshViewer:
    def __init__(self):
        rospy.init_node('yolo_ksh_viewer', anonymous=True)
        self.bridge = CvBridge()

        # 订阅ksh话题
        self.image_sub = rospy.Subscriber('/yolo_ksh', Image, self.image_callback)

        rospy.loginfo("[Ksh Viewer] 等待YOLO ksh图像...")
        rospy.loginfo("[Ksh Viewer] 按 'q' 键退出")
        
    def image_callback(self, msg):
        try:
            # 转换ROS图像到OpenCV格式
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            # 显示图像
            cv2.imshow('YOLO Detection Ksh', cv_image)

            # 等待按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                rospy.loginfo("[Ksh Viewer] 退出...")
                rospy.signal_shutdown("User requested shutdown")

        except Exception as e:
            rospy.logerr("[Ksh Viewer] 图像处理错误: %s", str(e))

    def run(self):
        try:
            rospy.spin()
        except KeyboardInterrupt:
            rospy.loginfo("[Ksh Viewer] 键盘中断，退出...")
        finally:
            cv2.destroyAllWindows()

if __name__ == '__main__':
    try:
        viewer = KshViewer()
        viewer.run()
    except rospy.ROSInterruptException:
        pass
    finally:
        cv2.destroyAllWindows()
