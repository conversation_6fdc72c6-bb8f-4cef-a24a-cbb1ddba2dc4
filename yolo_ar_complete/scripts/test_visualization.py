#!/usr/bin/env python3
"""
测试YOLO可视化功能的脚本
"""
import rospy
import cv2
from sensor_msgs.msg import Image
from cv_bridge import CvBridge

class VisualizationViewer:
    def __init__(self):
        rospy.init_node('yolo_visualization_viewer', anonymous=True)
        self.bridge = CvBridge()
        
        # 订阅可视化话题
        self.image_sub = rospy.Subscriber('/yolo_visualization', Image, self.image_callback)
        
        rospy.loginfo("[Visualization Viewer] 等待YOLO可视化图像...")
        rospy.loginfo("[Visualization Viewer] 按 'q' 键退出")
        
    def image_callback(self, msg):
        try:
            # 转换ROS图像到OpenCV格式
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            
            # 显示图像
            cv2.imshow('YOLO Detection Visualization', cv_image)
            
            # 等待按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                rospy.loginfo("[Visualization Viewer] 退出...")
                rospy.signal_shutdown("User requested shutdown")
                
        except Exception as e:
            rospy.logerr("[Visualization Viewer] 图像处理错误: %s", str(e))

    def run(self):
        try:
            rospy.spin()
        except KeyboardInterrupt:
            rospy.loginfo("[Visualization Viewer] 键盘中断，退出...")
        finally:
            cv2.destroyAllWindows()

if __name__ == '__main__':
    try:
        viewer = VisualizationViewer()
        viewer.run()
    except rospy.ROSInterruptException:
        pass
    finally:
        cv2.destroyAllWindows()
