#!/usr/bin/env python3
"""
使用静态图片测试YOLO ksh可视化功能
"""
import rospy
import cv2
import numpy as np
import os
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
from std_msgs.msg import Header

class ImageKshTester:
    def __init__(self):
        rospy.init_node('image_ksh_tester', anonymous=True)
        self.bridge = CvBridge()
        
        # 发布者 - 模拟相机图像
        self.image_pub = rospy.Publisher('/hand_camera/image_raw', Image, queue_size=1)
        
        # 订阅者 - 接收ksh结果
        self.ksh_sub = rospy.Subscriber('/yolo_ksh', Image, self.ksh_callback)
        
        # 图片路径
        self.image_path = os.path.expanduser('~/YOLO (1)/shiji.png')
        
        # 状态变量
        self.ksh_received = False
        self.ksh_image = None
        
        rospy.loginfo("[Image KSH Tester] 初始化完成")
        rospy.loginfo("[Image KSH Tester] 图片路径: %s", self.image_path)
        
    def ksh_callback(self, msg):
        """接收ksh图像的回调函数"""
        try:
            self.ksh_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            self.ksh_received = True
            rospy.loginfo("[Image KSH Tester] 收到ksh图像，尺寸: %dx%d", 
                         self.ksh_image.shape[1], self.ksh_image.shape[0])
        except Exception as e:
            rospy.logerr("[Image KSH Tester] ksh图像转换错误: %s", str(e))
    
    def load_test_image(self):
        """加载测试图片"""
        if not os.path.exists(self.image_path):
            rospy.logerr("[Image KSH Tester] 图片文件不存在: %s", self.image_path)
            return None
            
        try:
            image = cv2.imread(self.image_path)
            if image is None:
                rospy.logerr("[Image KSH Tester] 无法读取图片: %s", self.image_path)
                return None
                
            rospy.loginfo("[Image KSH Tester] 成功加载图片，尺寸: %dx%d", 
                         image.shape[1], image.shape[0])
            return image
            
        except Exception as e:
            rospy.logerr("[Image KSH Tester] 加载图片错误: %s", str(e))
            return None
    
    def publish_image(self, cv_image):
        """发布图像到ROS话题"""
        try:
            # 创建ROS图像消息
            header = Header()
            header.stamp = rospy.Time.now()
            header.frame_id = "hand_camera_optical_frame"
            
            ros_image = self.bridge.cv2_to_imgmsg(cv_image, "bgr8")
            ros_image.header = header
            
            # 发布图像
            self.image_pub.publish(ros_image)
            rospy.loginfo("[Image KSH Tester] 发布图像到 /hand_camera/image_raw")
            
        except Exception as e:
            rospy.logerr("[Image KSH Tester] 发布图像错误: %s", str(e))
    
    def display_results(self, original_image):
        """显示原图和ksh结果"""
        try:
            # 显示原图
            cv2.imshow('Original Image', original_image)
            
            # 如果收到了ksh图像，显示它
            if self.ksh_received and self.ksh_image is not None:
                cv2.imshow('YOLO KSH Result', self.ksh_image)
                rospy.loginfo("[Image KSH Tester] 显示ksh结果图像")
            else:
                rospy.logwarn("[Image KSH Tester] 未收到ksh图像")
            
            # 等待按键
            rospy.loginfo("[Image KSH Tester] 按任意键继续，按 'q' 退出")
            key = cv2.waitKey(0) & 0xFF
            
            return key != ord('q')
            
        except Exception as e:
            rospy.logerr("[Image KSH Tester] 显示结果错误: %s", str(e))
            return False
    
    def run_test(self):
        """运行测试"""
        rospy.loginfo("[Image KSH Tester] 开始测试...")
        
        # 加载测试图片
        test_image = self.load_test_image()
        if test_image is None:
            return False
        
        # 等待一下让节点启动
        rospy.loginfo("[Image KSH Tester] 等待YOLO节点启动...")
        rospy.sleep(2.0)
        
        # 发布图像
        rate = rospy.Rate(1)  # 1Hz
        for i in range(5):  # 发布5次
            if rospy.is_shutdown():
                break
                
            rospy.loginfo("[Image KSH Tester] 发布图像 %d/5", i+1)
            self.publish_image(test_image)
            rate.sleep()
            
            # 检查是否收到ksh结果
            if self.ksh_received:
                rospy.loginfo("[Image KSH Tester] ✅ 成功收到ksh图像!")
                break
        
        # 显示结果
        if self.ksh_received:
            return self.display_results(test_image)
        else:
            rospy.logwarn("[Image KSH Tester] ❌ 未收到ksh图像，请检查:")
            rospy.logwarn("  1. YOLO节点是否正在运行")
            rospy.logwarn("  2. enable_ksh参数是否为true")
            rospy.logwarn("  3. 模型文件是否存在")
            return False
    
    def cleanup(self):
        """清理资源"""
        cv2.destroyAllWindows()

def main():
    try:
        tester = ImageKshTester()
        
        # 检查必要的话题
        rospy.loginfo("[Image KSH Tester] 检查话题状态...")
        
        # 等待一下让话题列表更新
        rospy.sleep(1.0)
        
        # 运行测试
        success = tester.run_test()
        
        if success:
            rospy.loginfo("[Image KSH Tester] ✅ 测试完成!")
        else:
            rospy.loginfo("[Image KSH Tester] ❌ 测试失败!")
            
    except KeyboardInterrupt:
        rospy.loginfo("[Image KSH Tester] 用户中断测试")
    except Exception as e:
        rospy.logerr("[Image KSH Tester] 测试错误: %s", str(e))
    finally:
        if 'tester' in locals():
            tester.cleanup()

if __name__ == '__main__':
    main()
