# YOLO KSH功能使用说明

## 功能介绍

YOLO KSH功能可以将YOLO检测结果以带有识别框和标签的图像形式发布到ROS话题中。

## 主要特性

- ✅ 在原图像上绘制检测框
- ✅ 显示类别标签和置信度
- ✅ 不同物体类别使用不同颜色
- ✅ 显示物体中心点
- ✅ 简洁显示，无额外状态信息
- ✅ 可配置启用/禁用

## 话题说明

### 输出话题
- `/yolo_ksh` - 带检测框和标签的ksh图像 (sensor_msgs/Image)

### 颜色编码
- `jintou` (金头) - 绿色
- `mada` (马达) - 蓝色  
- `shext` (舌头) - 红色
- `xiaoxp` (小芯片) - 青色
- `xingpian` (芯片) - 紫色
- `zhuanzi` (转子) - 黄色

## 使用方法

### 1. 启动完整系统（包含ksh）
```bash
roslaunch yolo_ar_complete yolo_complete.launch enable_ksh:=true
```

### 2. 仅启动YOLO检测（不含ksh）
```bash
roslaunch yolo_ar_complete yolo_complete.launch enable_ksh:=false
```

### 3. 自定义ksh话题名称
```bash
roslaunch yolo_ar_complete yolo_complete.launch ksh_topic:=/my_yolo_ksh
```

### 4. 查看ksh图像
使用RViz：
```bash
# 在RViz中添加Image显示，订阅话题 /yolo_ksh
rviz
```

使用image_view：
```bash
rosrun image_view image_view image:=/yolo_ksh
```

使用自带的测试脚本：
```bash
rosrun yolo_ar_complete test_visualization.py
```

## 参数配置

在launch文件中可以配置以下参数：

```xml
<!-- 是否启用ksh -->
<arg name="enable_ksh" default="true" />

<!-- ksh话题名称 -->
<arg name="ksh_topic" default="/yolo_ksh" />
```

在Python节点中可以配置：

```yaml
# 在参数服务器中设置
/yolo_detector/enable_ksh: true
/yolo_detector/ksh_topic: "/yolo_ksh"
```

## 性能说明

- ksh功能会增加一定的计算开销
- 如果不需要ksh，建议设置 `enable_ksh:=false` 以提高性能
- ksh图像的发布频率与检测频率相同

## 故障排除

### 1. 没有ksh图像输出
- 检查 `enable_ksh` 参数是否为 `true`
- 检查话题是否正确：`rostopic list | grep yolo`

### 2. 图像显示异常
- 确认OpenCV正确安装：`python3 -c "import cv2; print(cv2.__version__)"`
- 检查cv_bridge是否正常工作

### 3. 颜色显示不正确
- 确认物体类别名称与预定义的类别匹配
- 检查检测结果中的 `class_name` 字段

## 测试命令

```bash
# 运行测试脚本检查所有话题
rosrun yolo_ar_complete test_yolo_visualization.sh

# 手动检查话题
rostopic list | grep yolo
rostopic hz /yolo_ksh
rostopic echo /yolo_detections -n 1
```
