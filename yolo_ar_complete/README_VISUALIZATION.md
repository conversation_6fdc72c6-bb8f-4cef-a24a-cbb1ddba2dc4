# YOLO可视化功能使用说明

## 功能介绍

新增的YOLO可视化功能可以将YOLO检测结果以带有识别框和标签的图像形式发布到ROS话题中，方便调试和监控。

## 主要特性

- ✅ 在原图像上绘制检测框
- ✅ 显示类别标签和置信度
- ✅ 不同物体类别使用不同颜色
- ✅ 显示物体中心点
- ✅ 显示检测数量统计
- ✅ 可配置启用/禁用

## 话题说明

### 输出话题
- `/yolo_visualization` - 带检测框和标签的可视化图像 (sensor_msgs/Image)

### 颜色编码
- `jintou` (金头) - 绿色
- `mada` (马达) - 蓝色  
- `shext` (舌头) - 红色
- `xiaoxp` (小芯片) - 青色
- `xingpian` (芯片) - 紫色
- `zhuanzi` (转子) - 黄色

## 使用方法

### 1. 启动完整系统（包含可视化）
```bash
roslaunch yolo_ar_complete yolo_complete.launch enable_visualization:=true
```

### 2. 仅启动YOLO检测（不含可视化）
```bash
roslaunch yolo_ar_complete yolo_complete.launch enable_visualization:=false
```

### 3. 自定义可视化话题名称
```bash
roslaunch yolo_ar_complete yolo_complete.launch visualization_topic:=/my_yolo_vis
```

### 4. 查看可视化图像
使用RViz：
```bash
# 在RViz中添加Image显示，订阅话题 /yolo_visualization
rviz
```

使用image_view：
```bash
rosrun image_view image_view image:=/yolo_visualization
```

使用自带的测试脚本：
```bash
rosrun yolo_ar_complete test_visualization.py
```

## 参数配置

在launch文件中可以配置以下参数：

```xml
<!-- 是否启用可视化 -->
<arg name="enable_visualization" default="true" />

<!-- 可视化话题名称 -->
<arg name="visualization_topic" default="/yolo_visualization" />
```

在Python节点中可以配置：

```yaml
# 在参数服务器中设置
/yolo_detector/enable_visualization: true
/yolo_detector/visualization_topic: "/yolo_visualization"
```

## 性能说明

- 可视化功能会增加一定的计算开销
- 如果不需要可视化，建议设置 `enable_visualization:=false` 以提高性能
- 可视化图像的发布频率与检测频率相同

## 故障排除

### 1. 没有可视化图像输出
- 检查 `enable_visualization` 参数是否为 `true`
- 确认有检测到物体（只有检测到物体时才发布可视化图像）
- 检查话题是否正确：`rostopic list | grep yolo`

### 2. 图像显示异常
- 确认OpenCV正确安装：`python3 -c "import cv2; print(cv2.__version__)"`
- 检查cv_bridge是否正常工作

### 3. 颜色显示不正确
- 确认物体类别名称与预定义的类别匹配
- 检查检测结果中的 `class_name` 字段

## 开发说明

如需修改颜色或添加新的物体类别，请编辑 `yolo_detector_python.py` 中的 `colors` 字典：

```python
colors = {
    'new_object': (B, G, R),  # BGR格式
    # 添加更多颜色...
}
```
