# YOLO可视化功能使用示例

## 快速开始

### 1. 启动系统
```bash
# 启动完整的YOLO-AR系统（包含可视化）
roslaunch yolo_ar_complete yolo_complete.launch enable_visualization:=true

# 或者仅启动YOLO检测（不含可视化，性能更好）
roslaunch yolo_ar_complete yolo_complete.launch enable_visualization:=false
```

### 2. 查看可视化结果

#### 方法1: 使用RViz
```bash
# 启动RViz
rviz

# 在RViz中:
# 1. 点击 "Add" 按钮
# 2. 选择 "Image" 
# 3. 在Image的设置中，将Topic设置为 "/yolo_visualization"
```

#### 方法2: 使用image_view
```bash
# 查看可视化图像
rosrun image_view image_view image:=/yolo_visualization
```

#### 方法3: 使用自带测试脚本
```bash
# 使用OpenCV窗口显示
rosrun yolo_ar_complete test_visualization.py
```

### 3. 检查系统状态
```bash
# 运行测试脚本检查所有话题
rosrun yolo_ar_complete test_yolo_visualization.sh

# 手动检查话题
rostopic list | grep yolo
rostopic hz /yolo_visualization
rostopic echo /yolo_detections -n 1
```

## 可视化效果说明

### 检测框颜色
- 🟢 **绿色**: jintou (金头)
- 🔵 **蓝色**: mada (马达)  
- 🔴 **红色**: shext (舌头)
- 🟡 **青色**: xiaoxp (小芯片)
- 🟣 **紫色**: xingpian (芯片)
- 🟡 **黄色**: zhuanzi (转子)

### 显示元素
- **边界框**: 围绕检测物体的彩色矩形
- **标签**: 显示类别名称和置信度 (如: "jintou: 0.85")
- **中心点**: 物体中心的小圆点
- **状态信息**: 左上角显示检测数量和系统状态

## 参数配置

### Launch文件参数
```bash
# 启用/禁用可视化
roslaunch yolo_ar_complete yolo_complete.launch enable_visualization:=true

# 自定义可视化话题
roslaunch yolo_ar_complete yolo_complete.launch visualization_topic:=/my_yolo_vis

# 调整检测阈值
roslaunch yolo_ar_complete yolo_complete.launch confidence_threshold:=0.7

# 指定模型路径
roslaunch yolo_ar_complete yolo_complete.launch model_path:=/path/to/your/model.pt
```

### 运行时参数
```bash
# 动态调整参数
rosparam set /yolo_detector/enable_visualization true
rosparam set /yolo_detector/confidence_threshold 0.6
```

## 故障排除

### 问题1: 没有可视化图像
```bash
# 检查可视化是否启用
rosparam get /yolo_detector/enable_visualization

# 检查话题是否存在
rostopic list | grep yolo_visualization

# 检查是否有相机输入
rostopic hz /hand_camera/image_raw
```

### 问题2: 检测不到物体
```bash
# 降低置信度阈值
roslaunch yolo_ar_complete yolo_complete.launch confidence_threshold:=0.3

# 检查模型文件
ls -la ~/ros_workspace/yolo_model/best.pt

# 查看检测日志
rostopic echo /yolo_detections
```

### 问题3: 图像显示异常
```bash
# 检查OpenCV安装
python3 -c "import cv2; print(cv2.__version__)"

# 检查cv_bridge
python3 -c "from cv_bridge import CvBridge; print('OK')"

# 重启节点
rosnode kill /yolo_detector
# 然后重新启动launch文件
```

## 性能优化

### 提高检测速度
```bash
# 禁用可视化
roslaunch yolo_ar_complete yolo_complete.launch enable_visualization:=false

# 提高置信度阈值
roslaunch yolo_ar_complete yolo_complete.launch confidence_threshold:=0.8

# 使用较小的输入尺寸
rosparam set /yolo_detector/input_size 416
```

### 降低CPU使用率
```bash
# 限制发布频率
rostopic pub /yolo_detector/max_frequency std_msgs/Float32 "data: 5.0"
```

## 集成到其他系统

### 订阅可视化图像
```python
import rospy
from sensor_msgs.msg import Image
from cv_bridge import CvBridge

def image_callback(msg):
    bridge = CvBridge()
    cv_image = bridge.imgmsg_to_cv2(msg, "bgr8")
    # 处理图像...

rospy.Subscriber('/yolo_visualization', Image, image_callback)
```

### 录制可视化视频
```bash
# 录制rosbag
rosbag record /yolo_visualization

# 转换为视频文件
rosrun image_view extract_images _sec_per_frame:=0.1 image:=/yolo_visualization
```
