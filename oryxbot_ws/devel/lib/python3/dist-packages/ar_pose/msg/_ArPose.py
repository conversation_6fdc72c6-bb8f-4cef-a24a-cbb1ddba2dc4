# This Python file uses the following encoding: utf-8
"""autogenerated by genpy from ar_pose/ArPose.msg. Do not edit."""
import codecs
import sys
python3 = True if sys.hexversion > 0x03000000 else False
import genpy
import struct

import geometry_msgs.msg

class ArPose(genpy.Message):
  _md5sum = "de174cac63b5f4f47785f23b578b7ac0"
  _type = "ar_pose/ArPose"
  _has_header = False  # flag to mark the presence of a Header object
  _full_text = """uint32 number
geometry_msgs/Point position
float64 roll
float64 pitch
float64 yaw
uint32 confidence
================================================================================
MSG: geometry_msgs/Point
# This contains the position of a point in free space
float64 x
float64 y
float64 z
"""
  __slots__ = ['number','position','roll','pitch','yaw','confidence']
  _slot_types = ['uint32','geometry_msgs/Point','float64','float64','float64','uint32']

  def __init__(self, *args, **kwds):
    """
    Constructor. Any message fields that are implicitly/explicitly
    set to None will be assigned a default value. The recommend
    use is keyword arguments as this is more robust to future message
    changes.  You cannot mix in-order arguments and keyword arguments.

    The available fields are:
       number,position,roll,pitch,yaw,confidence

    :param args: complete set of field values, in .msg order
    :param kwds: use keyword arguments corresponding to message field names
    to set specific fields.
    """
    if args or kwds:
      super(ArPose, self).__init__(*args, **kwds)
      # message fields cannot be None, assign default values for those that are
      if self.number is None:
        self.number = 0
      if self.position is None:
        self.position = geometry_msgs.msg.Point()
      if self.roll is None:
        self.roll = 0.
      if self.pitch is None:
        self.pitch = 0.
      if self.yaw is None:
        self.yaw = 0.
      if self.confidence is None:
        self.confidence = 0
    else:
      self.number = 0
      self.position = geometry_msgs.msg.Point()
      self.roll = 0.
      self.pitch = 0.
      self.yaw = 0.
      self.confidence = 0

  def _get_types(self):
    """
    internal API method
    """
    return self._slot_types

  def serialize(self, buff):
    """
    serialize message into buffer
    :param buff: buffer, ``StringIO``
    """
    try:
      _x = self
      buff.write(_get_struct_I6dI().pack(_x.number, _x.position.x, _x.position.y, _x.position.z, _x.roll, _x.pitch, _x.yaw, _x.confidence))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize(self, str):
    """
    unpack serialized message in str into this message instance
    :param str: byte array of serialized message, ``str``
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.position is None:
        self.position = geometry_msgs.msg.Point()
      end = 0
      _x = self
      start = end
      end += 56
      (_x.number, _x.position.x, _x.position.y, _x.position.z, _x.roll, _x.pitch, _x.yaw, _x.confidence,) = _get_struct_I6dI().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill


  def serialize_numpy(self, buff, numpy):
    """
    serialize message with numpy array types into buffer
    :param buff: buffer, ``StringIO``
    :param numpy: numpy python module
    """
    try:
      _x = self
      buff.write(_get_struct_I6dI().pack(_x.number, _x.position.x, _x.position.y, _x.position.z, _x.roll, _x.pitch, _x.yaw, _x.confidence))
    except struct.error as se: self._check_types(struct.error("%s: '%s' when writing '%s'" % (type(se), str(se), str(locals().get('_x', self)))))
    except TypeError as te: self._check_types(ValueError("%s: '%s' when writing '%s'" % (type(te), str(te), str(locals().get('_x', self)))))

  def deserialize_numpy(self, str, numpy):
    """
    unpack serialized message in str into this message instance using numpy for array types
    :param str: byte array of serialized message, ``str``
    :param numpy: numpy python module
    """
    if python3:
      codecs.lookup_error("rosmsg").msg_type = self._type
    try:
      if self.position is None:
        self.position = geometry_msgs.msg.Point()
      end = 0
      _x = self
      start = end
      end += 56
      (_x.number, _x.position.x, _x.position.y, _x.position.z, _x.roll, _x.pitch, _x.yaw, _x.confidence,) = _get_struct_I6dI().unpack(str[start:end])
      return self
    except struct.error as e:
      raise genpy.DeserializationError(e)  # most likely buffer underfill

_struct_I = genpy.struct_I
def _get_struct_I():
    global _struct_I
    return _struct_I
_struct_I6dI = None
def _get_struct_I6dI():
    global _struct_I6dI
    if _struct_I6dI is None:
        _struct_I6dI = struct.Struct("<I6dI")
    return _struct_I6dI
