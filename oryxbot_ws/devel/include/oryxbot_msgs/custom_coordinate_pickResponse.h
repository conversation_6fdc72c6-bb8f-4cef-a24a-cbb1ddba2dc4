// Generated by gencpp from file oryxbot_msgs/custom_coordinate_pickResponse.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PICKRESPONSE_H
#define ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PICKRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace oryxbot_msgs
{
template <class ContainerAllocator>
struct custom_coordinate_pickResponse_
{
  typedef custom_coordinate_pickResponse_<ContainerAllocator> Type;

  custom_coordinate_pickResponse_()
    : message()
    , success(false)  {
    }
  custom_coordinate_pickResponse_(const ContainerAllocator& _alloc)
    : message(_alloc)
    , success(false)  {
  (void)_alloc;
    }



   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _message_type;
  _message_type message;

   typedef uint8_t _success_type;
  _success_type success;





  typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> const> ConstPtr;

}; // struct custom_coordinate_pickResponse_

typedef ::oryxbot_msgs::custom_coordinate_pickResponse_<std::allocator<void> > custom_coordinate_pickResponse;

typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_pickResponse > custom_coordinate_pickResponsePtr;
typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_pickResponse const> custom_coordinate_pickResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator2> & rhs)
{
  return lhs.message == rhs.message &&
    lhs.success == rhs.success;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace oryxbot_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "9bf829f07d795d3f9e541a07897da2c4";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x9bf829f07d795d3fULL;
  static const uint64_t static_value2 = 0x9e541a07897da2c4ULL;
};

template<class ContainerAllocator>
struct DataType< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "oryxbot_msgs/custom_coordinate_pickResponse";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "string message\n"
"bool success\n"
"\n"
;
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.message);
      stream.next(m.success);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct custom_coordinate_pickResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::oryxbot_msgs::custom_coordinate_pickResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "message: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.message);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "success: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.success);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PICKRESPONSE_H
