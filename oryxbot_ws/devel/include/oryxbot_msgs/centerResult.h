// Generated by gencpp from file oryxbot_msgs/centerResult.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_CENTERRESULT_H
#define ORYXBOT_MSGS_MESSAGE_CENTERRESULT_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace oryxbot_msgs
{
template <class ContainerAllocator>
struct centerResult_
{
  typedef centerResult_<ContainerAllocator> Type;

  centerResult_()
    : result(0)  {
    }
  centerResult_(const ContainerAllocator& _alloc)
    : result(0)  {
  (void)_alloc;
    }



   typedef uint8_t _result_type;
  _result_type result;





  typedef boost::shared_ptr< ::oryxbot_msgs::centerResult_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::oryxbot_msgs::centerResult_<ContainerAllocator> const> ConstPtr;

}; // struct centerResult_

typedef ::oryxbot_msgs::centerResult_<std::allocator<void> > centerResult;

typedef boost::shared_ptr< ::oryxbot_msgs::centerResult > centerResultPtr;
typedef boost::shared_ptr< ::oryxbot_msgs::centerResult const> centerResultConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::oryxbot_msgs::centerResult_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::oryxbot_msgs::centerResult_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::oryxbot_msgs::centerResult_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::centerResult_<ContainerAllocator2> & rhs)
{
  return lhs.result == rhs.result;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::oryxbot_msgs::centerResult_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::centerResult_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace oryxbot_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::centerResult_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::centerResult_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::centerResult_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "25458147911545c320c4c0a299eff763";
  }

  static const char* value(const ::oryxbot_msgs::centerResult_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x25458147911545c3ULL;
  static const uint64_t static_value2 = 0x20c4c0a299eff763ULL;
};

template<class ContainerAllocator>
struct DataType< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "oryxbot_msgs/centerResult";
  }

  static const char* value(const ::oryxbot_msgs::centerResult_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
{
  static const char* value()
  {
    return "# ====== DO NOT MODIFY! AUTOGENERATED FROM AN ACTION DEFINITION ======\n"
"uint8 result\n"
;
  }

  static const char* value(const ::oryxbot_msgs::centerResult_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.result);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct centerResult_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::oryxbot_msgs::centerResult_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::oryxbot_msgs::centerResult_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "result: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.result);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_CENTERRESULT_H
