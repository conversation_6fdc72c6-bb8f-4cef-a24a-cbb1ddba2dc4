// Generated by gencpp from file oryxbot_msgs/custom_coordinate_place.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PLACE_H
#define ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PLACE_H

#include <ros/service_traits.h>


#include <oryxbot_msgs/custom_coordinate_placeRequest.h>
#include <oryxbot_msgs/custom_coordinate_placeResponse.h>


namespace oryxbot_msgs
{

struct custom_coordinate_place
{

typedef custom_coordinate_placeRequest Request;
typedef custom_coordinate_placeResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct custom_coordinate_place
} // namespace oryxbot_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_place > {
  static const char* value()
  {
    return "0f534b00edc30d46085c83a104f26b15";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_place&) { return value(); }
};

template<>
struct DataType< ::oryxbot_msgs::custom_coordinate_place > {
  static const char* value()
  {
    return "oryxbot_msgs/custom_coordinate_place";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_place&) { return value(); }
};


// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_placeRequest> should match
// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_place >
template<>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_placeRequest>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::custom_coordinate_place >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_placeRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_placeRequest> should match
// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_place >
template<>
struct DataType< ::oryxbot_msgs::custom_coordinate_placeRequest>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::custom_coordinate_place >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_placeRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_placeResponse> should match
// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_place >
template<>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_placeResponse>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::custom_coordinate_place >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_placeResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_placeResponse> should match
// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_place >
template<>
struct DataType< ::oryxbot_msgs::custom_coordinate_placeResponse>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::custom_coordinate_place >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_placeResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PLACE_H
