// Generated by gencpp from file oryxbot_msgs/custom_coordinate_placeRequest.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PLACEREQUEST_H
#define ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PLACEREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Point.h>

namespace oryxbot_msgs
{
template <class ContainerAllocator>
struct custom_coordinate_placeRequest_
{
  typedef custom_coordinate_placeRequest_<ContainerAllocator> Type;

  custom_coordinate_placeRequest_()
    : workbench_point()
    , execute(false)  {
    }
  custom_coordinate_placeRequest_(const ContainerAllocator& _alloc)
    : workbench_point(_alloc)
    , execute(false)  {
  (void)_alloc;
    }



   typedef  ::geometry_msgs::Point_<ContainerAllocator>  _workbench_point_type;
  _workbench_point_type workbench_point;

   typedef uint8_t _execute_type;
  _execute_type execute;





  typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> const> ConstPtr;

}; // struct custom_coordinate_placeRequest_

typedef ::oryxbot_msgs::custom_coordinate_placeRequest_<std::allocator<void> > custom_coordinate_placeRequest;

typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_placeRequest > custom_coordinate_placeRequestPtr;
typedef boost::shared_ptr< ::oryxbot_msgs::custom_coordinate_placeRequest const> custom_coordinate_placeRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator2> & rhs)
{
  return lhs.workbench_point == rhs.workbench_point &&
    lhs.execute == rhs.execute;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace oryxbot_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "0fe85f56088963a6eff59af8a42466cc";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x0fe85f56088963a6ULL;
  static const uint64_t static_value2 = 0xeff59af8a42466ccULL;
};

template<class ContainerAllocator>
struct DataType< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "oryxbot_msgs/custom_coordinate_placeRequest";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "geometry_msgs/Point workbench_point   #机器人坐标系下的坐标\n"
"bool execute   #是否执行放置动作\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Point\n"
"# This contains the position of a point in free space\n"
"float64 x\n"
"float64 y\n"
"float64 z\n"
;
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.workbench_point);
      stream.next(m.execute);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct custom_coordinate_placeRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::oryxbot_msgs::custom_coordinate_placeRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "workbench_point: ";
    Printer< ::geometry_msgs::Point_<ContainerAllocator> >::stream(s, indent + "  ", v.workbench_point);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "execute: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.execute);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PLACEREQUEST_H
