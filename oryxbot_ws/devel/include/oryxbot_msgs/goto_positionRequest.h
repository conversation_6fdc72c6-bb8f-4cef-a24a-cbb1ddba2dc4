// Generated by gencpp from file oryxbot_msgs/goto_positionRequest.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_GOTO_POSITIONREQUEST_H
#define ORYXBOT_MSGS_MESSAGE_GOTO_POSITIONREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace oryxbot_msgs
{
template <class ContainerAllocator>
struct goto_positionRequest_
{
  typedef goto_positionRequest_<ContainerAllocator> Type;

  goto_positionRequest_()
    : x(0.0)
    , y(0.0)
    , z(0.0)
    , r(0.0)  {
    }
  goto_positionRequest_(const ContainerAllocator& _alloc)
    : x(0.0)
    , y(0.0)
    , z(0.0)
    , r(0.0)  {
  (void)_alloc;
    }



   typedef double _x_type;
  _x_type x;

   typedef double _y_type;
  _y_type y;

   typedef double _z_type;
  _z_type z;

   typedef double _r_type;
  _r_type r;





  typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> const> ConstPtr;

}; // struct goto_positionRequest_

typedef ::oryxbot_msgs::goto_positionRequest_<std::allocator<void> > goto_positionRequest;

typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionRequest > goto_positionRequestPtr;
typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionRequest const> goto_positionRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator2> & rhs)
{
  return lhs.x == rhs.x &&
    lhs.y == rhs.y &&
    lhs.z == rhs.z &&
    lhs.r == rhs.r;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace oryxbot_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "10953317a67672f91c603c89e23b17f3";
  }

  static const char* value(const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x10953317a67672f9ULL;
  static const uint64_t static_value2 = 0x1c603c89e23b17f3ULL;
};

template<class ContainerAllocator>
struct DataType< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "oryxbot_msgs/goto_positionRequest";
  }

  static const char* value(const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "float64 x\n"
"float64 y\n"
"float64 z\n"
"float64 r\n"
;
  }

  static const char* value(const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.x);
      stream.next(m.y);
      stream.next(m.z);
      stream.next(m.r);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct goto_positionRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::oryxbot_msgs::goto_positionRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "x: ";
    Printer<double>::stream(s, indent + "  ", v.x);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "y: ";
    Printer<double>::stream(s, indent + "  ", v.y);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "z: ";
    Printer<double>::stream(s, indent + "  ", v.z);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "r: ";
    Printer<double>::stream(s, indent + "  ", v.r);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_GOTO_POSITIONREQUEST_H
