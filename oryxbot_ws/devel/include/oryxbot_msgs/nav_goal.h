// Generated by gencpp from file oryxbot_msgs/nav_goal.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_NAV_GOAL_H
#define ORYXBOT_MSGS_MESSAGE_NAV_GOAL_H

#include <ros/service_traits.h>


#include <oryxbot_msgs/nav_goalRequest.h>
#include <oryxbot_msgs/nav_goalResponse.h>


namespace oryxbot_msgs
{

struct nav_goal
{

typedef nav_goalRequest Request;
typedef nav_goalResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct nav_goal
} // namespace oryxbot_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::oryxbot_msgs::nav_goal > {
  static const char* value()
  {
    return "3bd059df70d5e80971945ad044457a3c";
  }

  static const char* value(const ::oryxbot_msgs::nav_goal&) { return value(); }
};

template<>
struct DataType< ::oryxbot_msgs::nav_goal > {
  static const char* value()
  {
    return "oryxbot_msgs/nav_goal";
  }

  static const char* value(const ::oryxbot_msgs::nav_goal&) { return value(); }
};


// service_traits::MD5Sum< ::oryxbot_msgs::nav_goalRequest> should match
// service_traits::MD5Sum< ::oryxbot_msgs::nav_goal >
template<>
struct MD5Sum< ::oryxbot_msgs::nav_goalRequest>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::nav_goal >::value();
  }
  static const char* value(const ::oryxbot_msgs::nav_goalRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::nav_goalRequest> should match
// service_traits::DataType< ::oryxbot_msgs::nav_goal >
template<>
struct DataType< ::oryxbot_msgs::nav_goalRequest>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::nav_goal >::value();
  }
  static const char* value(const ::oryxbot_msgs::nav_goalRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::oryxbot_msgs::nav_goalResponse> should match
// service_traits::MD5Sum< ::oryxbot_msgs::nav_goal >
template<>
struct MD5Sum< ::oryxbot_msgs::nav_goalResponse>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::nav_goal >::value();
  }
  static const char* value(const ::oryxbot_msgs::nav_goalResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::nav_goalResponse> should match
// service_traits::DataType< ::oryxbot_msgs::nav_goal >
template<>
struct DataType< ::oryxbot_msgs::nav_goalResponse>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::nav_goal >::value();
  }
  static const char* value(const ::oryxbot_msgs::nav_goalResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_NAV_GOAL_H
