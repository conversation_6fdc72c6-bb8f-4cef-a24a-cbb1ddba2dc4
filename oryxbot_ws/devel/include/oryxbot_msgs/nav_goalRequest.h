// Generated by gencpp from file oryxbot_msgs/nav_goalRequest.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_NAV_GOALREQUEST_H
#define ORYXBOT_MSGS_MESSAGE_NAV_GOALREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>

#include <geometry_msgs/Pose2D.h>

namespace oryxbot_msgs
{
template <class ContainerAllocator>
struct nav_goalRequest_
{
  typedef nav_goalRequest_<ContainerAllocator> Type;

  nav_goalRequest_()
    : pose()  {
    }
  nav_goalRequest_(const ContainerAllocator& _alloc)
    : pose(_alloc)  {
  (void)_alloc;
    }



   typedef  ::geometry_msgs::Pose2D_<ContainerAllocator>  _pose_type;
  _pose_type pose;





  typedef boost::shared_ptr< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> const> ConstPtr;

}; // struct nav_goalRequest_

typedef ::oryxbot_msgs::nav_goalRequest_<std::allocator<void> > nav_goalRequest;

typedef boost::shared_ptr< ::oryxbot_msgs::nav_goalRequest > nav_goalRequestPtr;
typedef boost::shared_ptr< ::oryxbot_msgs::nav_goalRequest const> nav_goalRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator2> & rhs)
{
  return lhs.pose == rhs.pose;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace oryxbot_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "271cb12677c4cd9bccbc642cd9258d1f";
  }

  static const char* value(const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x271cb12677c4cd9bULL;
  static const uint64_t static_value2 = 0xccbc642cd9258d1fULL;
};

template<class ContainerAllocator>
struct DataType< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "oryxbot_msgs/nav_goalRequest";
  }

  static const char* value(const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "geometry_msgs/Pose2D pose\n"
"\n"
"================================================================================\n"
"MSG: geometry_msgs/Pose2D\n"
"# Deprecated\n"
"# Please use the full 3D pose.\n"
"\n"
"# In general our recommendation is to use a full 3D representation of everything and for 2D specific applications make the appropriate projections into the plane for their calculations but optimally will preserve the 3D information during processing.\n"
"\n"
"# If we have parallel copies of 2D datatypes every UI and other pipeline will end up needing to have dual interfaces to plot everything. And you will end up with not being able to use 3D tools for 2D use cases even if they're completely valid, as you'd have to reimplement it with different inputs and outputs. It's not particularly hard to plot the 2D pose or compute the yaw error for the Pose message and there are already tools and libraries that can do this for you.\n"
"\n"
"\n"
"# This expresses a position and orientation on a 2D manifold.\n"
"\n"
"float64 x\n"
"float64 y\n"
"float64 theta\n"
;
  }

  static const char* value(const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.pose);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct nav_goalRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::oryxbot_msgs::nav_goalRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "pose: ";
    Printer< ::geometry_msgs::Pose2D_<ContainerAllocator> >::stream(s, indent + "  ", v.pose);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_NAV_GOALREQUEST_H
