// Generated by gencpp from file oryxbot_msgs/goto_positionResponse.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_GOTO_POSITIONRESPONSE_H
#define ORYXBOT_MSGS_MESSAGE_GOTO_POSITIONRESPONSE_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace oryxbot_msgs
{
template <class ContainerAllocator>
struct goto_positionResponse_
{
  typedef goto_positionResponse_<ContainerAllocator> Type;

  goto_positionResponse_()
    : ret(false)  {
    }
  goto_positionResponse_(const ContainerAllocator& _alloc)
    : ret(false)  {
  (void)_alloc;
    }



   typedef uint8_t _ret_type;
  _ret_type ret;





  typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> const> ConstPtr;

}; // struct goto_positionResponse_

typedef ::oryxbot_msgs::goto_positionResponse_<std::allocator<void> > goto_positionResponse;

typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionResponse > goto_positionResponsePtr;
typedef boost::shared_ptr< ::oryxbot_msgs::goto_positionResponse const> goto_positionResponseConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator2> & rhs)
{
  return lhs.ret == rhs.ret;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace oryxbot_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "e2cc9e9d8c464550830df49c160979ad";
  }

  static const char* value(const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0xe2cc9e9d8c464550ULL;
  static const uint64_t static_value2 = 0x830df49c160979adULL;
};

template<class ContainerAllocator>
struct DataType< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "oryxbot_msgs/goto_positionResponse";
  }

  static const char* value(const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool ret\n"
"\n"
;
  }

  static const char* value(const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.ret);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct goto_positionResponse_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::oryxbot_msgs::goto_positionResponse_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "ret: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.ret);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_GOTO_POSITIONRESPONSE_H
