// Generated by gencpp from file oryxbot_msgs/get_zero.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_GET_ZERO_H
#define ORYXBOT_MSGS_MESSAGE_GET_ZERO_H

#include <ros/service_traits.h>


#include <oryxbot_msgs/get_zeroRequest.h>
#include <oryxbot_msgs/get_zeroResponse.h>


namespace oryxbot_msgs
{

struct get_zero
{

typedef get_zeroRequest Request;
typedef get_zeroResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct get_zero
} // namespace oryxbot_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::oryxbot_msgs::get_zero > {
  static const char* value()
  {
    return "ff377af8a179ae6497369559e4036478";
  }

  static const char* value(const ::oryxbot_msgs::get_zero&) { return value(); }
};

template<>
struct DataType< ::oryxbot_msgs::get_zero > {
  static const char* value()
  {
    return "oryxbot_msgs/get_zero";
  }

  static const char* value(const ::oryxbot_msgs::get_zero&) { return value(); }
};


// service_traits::MD5Sum< ::oryxbot_msgs::get_zeroRequest> should match
// service_traits::MD5Sum< ::oryxbot_msgs::get_zero >
template<>
struct MD5Sum< ::oryxbot_msgs::get_zeroRequest>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::get_zero >::value();
  }
  static const char* value(const ::oryxbot_msgs::get_zeroRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::get_zeroRequest> should match
// service_traits::DataType< ::oryxbot_msgs::get_zero >
template<>
struct DataType< ::oryxbot_msgs::get_zeroRequest>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::get_zero >::value();
  }
  static const char* value(const ::oryxbot_msgs::get_zeroRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::oryxbot_msgs::get_zeroResponse> should match
// service_traits::MD5Sum< ::oryxbot_msgs::get_zero >
template<>
struct MD5Sum< ::oryxbot_msgs::get_zeroResponse>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::get_zero >::value();
  }
  static const char* value(const ::oryxbot_msgs::get_zeroResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::get_zeroResponse> should match
// service_traits::DataType< ::oryxbot_msgs::get_zero >
template<>
struct DataType< ::oryxbot_msgs::get_zeroResponse>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::get_zero >::value();
  }
  static const char* value(const ::oryxbot_msgs::get_zeroResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_GET_ZERO_H
