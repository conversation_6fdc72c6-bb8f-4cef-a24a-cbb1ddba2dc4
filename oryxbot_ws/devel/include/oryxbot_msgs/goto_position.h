// Generated by gencpp from file oryxbot_msgs/goto_position.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_GOTO_POSITION_H
#define ORYXBOT_MSGS_MESSAGE_GOTO_POSITION_H

#include <ros/service_traits.h>


#include <oryxbot_msgs/goto_positionRequest.h>
#include <oryxbot_msgs/goto_positionResponse.h>


namespace oryxbot_msgs
{

struct goto_position
{

typedef goto_positionRequest Request;
typedef goto_positionResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct goto_position
} // namespace oryxbot_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::oryxbot_msgs::goto_position > {
  static const char* value()
  {
    return "5b8382ba19e04cc4990f05b8d6c39e7b";
  }

  static const char* value(const ::oryxbot_msgs::goto_position&) { return value(); }
};

template<>
struct DataType< ::oryxbot_msgs::goto_position > {
  static const char* value()
  {
    return "oryxbot_msgs/goto_position";
  }

  static const char* value(const ::oryxbot_msgs::goto_position&) { return value(); }
};


// service_traits::MD5Sum< ::oryxbot_msgs::goto_positionRequest> should match
// service_traits::MD5Sum< ::oryxbot_msgs::goto_position >
template<>
struct MD5Sum< ::oryxbot_msgs::goto_positionRequest>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::goto_position >::value();
  }
  static const char* value(const ::oryxbot_msgs::goto_positionRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::goto_positionRequest> should match
// service_traits::DataType< ::oryxbot_msgs::goto_position >
template<>
struct DataType< ::oryxbot_msgs::goto_positionRequest>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::goto_position >::value();
  }
  static const char* value(const ::oryxbot_msgs::goto_positionRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::oryxbot_msgs::goto_positionResponse> should match
// service_traits::MD5Sum< ::oryxbot_msgs::goto_position >
template<>
struct MD5Sum< ::oryxbot_msgs::goto_positionResponse>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::goto_position >::value();
  }
  static const char* value(const ::oryxbot_msgs::goto_positionResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::goto_positionResponse> should match
// service_traits::DataType< ::oryxbot_msgs::goto_position >
template<>
struct DataType< ::oryxbot_msgs::goto_positionResponse>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::goto_position >::value();
  }
  static const char* value(const ::oryxbot_msgs::goto_positionResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_GOTO_POSITION_H
