// Generated by gencpp from file oryxbot_msgs/get_zeroRequest.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_GET_ZEROREQUEST_H
#define ORYXBOT_MSGS_MESSAGE_GET_ZEROREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace oryxbot_msgs
{
template <class ContainerAllocator>
struct get_zeroRequest_
{
  typedef get_zeroRequest_<ContainerAllocator> Type;

  get_zeroRequest_()
    : query_flag(false)  {
    }
  get_zeroRequest_(const ContainerAllocator& _alloc)
    : query_flag(false)  {
  (void)_alloc;
    }



   typedef uint8_t _query_flag_type;
  _query_flag_type query_flag;





  typedef boost::shared_ptr< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> const> ConstPtr;

}; // struct get_zeroRequest_

typedef ::oryxbot_msgs::get_zeroRequest_<std::allocator<void> > get_zeroRequest;

typedef boost::shared_ptr< ::oryxbot_msgs::get_zeroRequest > get_zeroRequestPtr;
typedef boost::shared_ptr< ::oryxbot_msgs::get_zeroRequest const> get_zeroRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator2> & rhs)
{
  return lhs.query_flag == rhs.query_flag;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator1> & lhs, const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace oryxbot_msgs

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "156edc5080f8db678eb8ca44b604d0ff";
  }

  static const char* value(const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x156edc5080f8db67ULL;
  static const uint64_t static_value2 = 0x8eb8ca44b604d0ffULL;
};

template<class ContainerAllocator>
struct DataType< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "oryxbot_msgs/get_zeroRequest";
  }

  static const char* value(const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "bool query_flag\n"
;
  }

  static const char* value(const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.query_flag);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct get_zeroRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::oryxbot_msgs::get_zeroRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "query_flag: ";
    Printer<uint8_t>::stream(s, indent + "  ", v.query_flag);
  }
};

} // namespace message_operations
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_GET_ZEROREQUEST_H
