// Generated by gencpp from file oryxbot_msgs/custom_coordinate_pick.msg
// DO NOT EDIT!


#ifndef ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PICK_H
#define ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PICK_H

#include <ros/service_traits.h>


#include <oryxbot_msgs/custom_coordinate_pickRequest.h>
#include <oryxbot_msgs/custom_coordinate_pickResponse.h>


namespace oryxbot_msgs
{

struct custom_coordinate_pick
{

typedef custom_coordinate_pickRequest Request;
typedef custom_coordinate_pickResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct custom_coordinate_pick
} // namespace oryxbot_msgs


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_pick > {
  static const char* value()
  {
    return "0f534b00edc30d46085c83a104f26b15";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_pick&) { return value(); }
};

template<>
struct DataType< ::oryxbot_msgs::custom_coordinate_pick > {
  static const char* value()
  {
    return "oryxbot_msgs/custom_coordinate_pick";
  }

  static const char* value(const ::oryxbot_msgs::custom_coordinate_pick&) { return value(); }
};


// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_pickRequest> should match
// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_pick >
template<>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_pickRequest>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::custom_coordinate_pick >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_pickRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_pickRequest> should match
// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_pick >
template<>
struct DataType< ::oryxbot_msgs::custom_coordinate_pickRequest>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::custom_coordinate_pick >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_pickRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_pickResponse> should match
// service_traits::MD5Sum< ::oryxbot_msgs::custom_coordinate_pick >
template<>
struct MD5Sum< ::oryxbot_msgs::custom_coordinate_pickResponse>
{
  static const char* value()
  {
    return MD5Sum< ::oryxbot_msgs::custom_coordinate_pick >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_pickResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_pickResponse> should match
// service_traits::DataType< ::oryxbot_msgs::custom_coordinate_pick >
template<>
struct DataType< ::oryxbot_msgs::custom_coordinate_pickResponse>
{
  static const char* value()
  {
    return DataType< ::oryxbot_msgs::custom_coordinate_pick >::value();
  }
  static const char* value(const ::oryxbot_msgs::custom_coordinate_pickResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // ORYXBOT_MSGS_MESSAGE_CUSTOM_COORDINATE_PICK_H
