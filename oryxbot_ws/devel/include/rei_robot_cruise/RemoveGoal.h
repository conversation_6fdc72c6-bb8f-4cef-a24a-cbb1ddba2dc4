// Generated by gencpp from file rei_robot_cruise/RemoveGoal.msg
// DO NOT EDIT!


#ifndef REI_ROBOT_CRUISE_MESSAGE_REMOVEGOAL_H
#define REI_ROBOT_CRUISE_MESSAGE_REMOVEGOAL_H

#include <ros/service_traits.h>


#include <rei_robot_cruise/RemoveGoalRequest.h>
#include <rei_robot_cruise/RemoveGoalResponse.h>


namespace rei_robot_cruise
{

struct RemoveGoal
{

typedef RemoveGoalRequest Request;
typedef RemoveGoalResponse Response;
Request request;
Response response;

typedef Request RequestType;
typedef Response ResponseType;

}; // struct RemoveGoal
} // namespace rei_robot_cruise


namespace ros
{
namespace service_traits
{


template<>
struct MD5Sum< ::rei_robot_cruise::RemoveGoal > {
  static const char* value()
  {
    return "d1f169e29b016407be028dd4d3bd5a79";
  }

  static const char* value(const ::rei_robot_cruise::RemoveGoal&) { return value(); }
};

template<>
struct DataType< ::rei_robot_cruise::RemoveGoal > {
  static const char* value()
  {
    return "rei_robot_cruise/RemoveGoal";
  }

  static const char* value(const ::rei_robot_cruise::RemoveGoal&) { return value(); }
};


// service_traits::MD5Sum< ::rei_robot_cruise::RemoveGoalRequest> should match
// service_traits::MD5Sum< ::rei_robot_cruise::RemoveGoal >
template<>
struct MD5Sum< ::rei_robot_cruise::RemoveGoalRequest>
{
  static const char* value()
  {
    return MD5Sum< ::rei_robot_cruise::RemoveGoal >::value();
  }
  static const char* value(const ::rei_robot_cruise::RemoveGoalRequest&)
  {
    return value();
  }
};

// service_traits::DataType< ::rei_robot_cruise::RemoveGoalRequest> should match
// service_traits::DataType< ::rei_robot_cruise::RemoveGoal >
template<>
struct DataType< ::rei_robot_cruise::RemoveGoalRequest>
{
  static const char* value()
  {
    return DataType< ::rei_robot_cruise::RemoveGoal >::value();
  }
  static const char* value(const ::rei_robot_cruise::RemoveGoalRequest&)
  {
    return value();
  }
};

// service_traits::MD5Sum< ::rei_robot_cruise::RemoveGoalResponse> should match
// service_traits::MD5Sum< ::rei_robot_cruise::RemoveGoal >
template<>
struct MD5Sum< ::rei_robot_cruise::RemoveGoalResponse>
{
  static const char* value()
  {
    return MD5Sum< ::rei_robot_cruise::RemoveGoal >::value();
  }
  static const char* value(const ::rei_robot_cruise::RemoveGoalResponse&)
  {
    return value();
  }
};

// service_traits::DataType< ::rei_robot_cruise::RemoveGoalResponse> should match
// service_traits::DataType< ::rei_robot_cruise::RemoveGoal >
template<>
struct DataType< ::rei_robot_cruise::RemoveGoalResponse>
{
  static const char* value()
  {
    return DataType< ::rei_robot_cruise::RemoveGoal >::value();
  }
  static const char* value(const ::rei_robot_cruise::RemoveGoalResponse&)
  {
    return value();
  }
};

} // namespace service_traits
} // namespace ros

#endif // REI_ROBOT_CRUISE_MESSAGE_REMOVEGOAL_H
