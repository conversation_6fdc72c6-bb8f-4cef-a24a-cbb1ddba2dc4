// Generated by gencpp from file rei_robot_cruise/LoadTaskRequest.msg
// DO NOT EDIT!


#ifndef REI_ROBOT_CRUISE_MESSAGE_LOADTASKREQUEST_H
#define REI_ROBOT_CRUISE_MESSAGE_LOADTASKREQUEST_H


#include <string>
#include <vector>
#include <memory>

#include <ros/types.h>
#include <ros/serialization.h>
#include <ros/builtin_message_traits.h>
#include <ros/message_operations.h>


namespace rei_robot_cruise
{
template <class ContainerAllocator>
struct LoadTaskRequest_
{
  typedef LoadTaskRequest_<ContainerAllocator> Type;

  LoadTaskRequest_()
    : type(0)
    , task_file()  {
    }
  LoadTaskRequest_(const ContainerAllocator& _alloc)
    : type(0)
    , task_file(_alloc)  {
  (void)_alloc;
    }



   typedef int8_t _type_type;
  _type_type type;

   typedef std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> _task_file_type;
  _task_file_type task_file;





  typedef boost::shared_ptr< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> > Ptr;
  typedef boost::shared_ptr< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> const> ConstPtr;

}; // struct LoadTaskRequest_

typedef ::rei_robot_cruise::LoadTaskRequest_<std::allocator<void> > LoadTaskRequest;

typedef boost::shared_ptr< ::rei_robot_cruise::LoadTaskRequest > LoadTaskRequestPtr;
typedef boost::shared_ptr< ::rei_robot_cruise::LoadTaskRequest const> LoadTaskRequestConstPtr;

// constants requiring out of line definition



template<typename ContainerAllocator>
std::ostream& operator<<(std::ostream& s, const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> & v)
{
ros::message_operations::Printer< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >::stream(s, "", v);
return s;
}


template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator==(const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator1> & lhs, const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator2> & rhs)
{
  return lhs.type == rhs.type &&
    lhs.task_file == rhs.task_file;
}

template<typename ContainerAllocator1, typename ContainerAllocator2>
bool operator!=(const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator1> & lhs, const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator2> & rhs)
{
  return !(lhs == rhs);
}


} // namespace rei_robot_cruise

namespace ros
{
namespace message_traits
{





template <class ContainerAllocator>
struct IsMessage< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
  : TrueType
  { };

template <class ContainerAllocator>
struct IsMessage< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> const>
  : TrueType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct IsFixedSize< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> const>
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
  : FalseType
  { };

template <class ContainerAllocator>
struct HasHeader< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> const>
  : FalseType
  { };


template<class ContainerAllocator>
struct MD5Sum< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "1bf3dbdcf529b753a457bd0e81f343bd";
  }

  static const char* value(const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator>&) { return value(); }
  static const uint64_t static_value1 = 0x1bf3dbdcf529b753ULL;
  static const uint64_t static_value2 = 0xa457bd0e81f343bdULL;
};

template<class ContainerAllocator>
struct DataType< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "rei_robot_cruise/LoadTaskRequest";
  }

  static const char* value(const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator>&) { return value(); }
};

template<class ContainerAllocator>
struct Definition< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
{
  static const char* value()
  {
    return "int8 type #0: auto, 1: yaml, 2:json\n"
"string task_file\n"
;
  }

  static const char* value(const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator>&) { return value(); }
};

} // namespace message_traits
} // namespace ros

namespace ros
{
namespace serialization
{

  template<class ContainerAllocator> struct Serializer< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
  {
    template<typename Stream, typename T> inline static void allInOne(Stream& stream, T m)
    {
      stream.next(m.type);
      stream.next(m.task_file);
    }

    ROS_DECLARE_ALLINONE_SERIALIZER
  }; // struct LoadTaskRequest_

} // namespace serialization
} // namespace ros

namespace ros
{
namespace message_operations
{

template<class ContainerAllocator>
struct Printer< ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator> >
{
  template<typename Stream> static void stream(Stream& s, const std::string& indent, const ::rei_robot_cruise::LoadTaskRequest_<ContainerAllocator>& v)
  {
    if (false || !indent.empty())
      s << std::endl;
    s << indent << "type: ";
    Printer<int8_t>::stream(s, indent + "  ", v.type);
    if (true || !indent.empty())
      s << std::endl;
    s << indent << "task_file: ";
    Printer<std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>>::stream(s, indent + "  ", v.task_file);
  }
};

} // namespace message_operations
} // namespace ros

#endif // REI_ROBOT_CRUISE_MESSAGE_LOADTASKREQUEST_H
